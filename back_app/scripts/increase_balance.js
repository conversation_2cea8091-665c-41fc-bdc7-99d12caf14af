#!/usr/bin/env node

const mongoose = require('mongoose');
const User = require('../src/models/User');

// MongoDB connection string
const MONGODB_URI = 'mongodb+srv://mstorsulam786:<EMAIL>/zero_koin';

/**
 * Increase user balance by email
 * @param {string} email - User's email address
 * @param {number} amount - Amount to add to balance
 */
async function increaseUserBalance(email, amount) {
  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('Connected to MongoDB successfully');

    // Find user by email
    console.log(`Looking for user with email: ${email}`);
    const user = await User.findOne({ email: email });

    if (!user) {
      console.log(`❌ User with email "${email}" not found`);
      return false;
    }

    // Get current balance
    const currentBalance = user.balance || 0;
    const newBalance = currentBalance + amount;

    // Update user balance
    await User.updateOne(
      { email: email },
      { 
        $inc: { balance: amount },
        $set: { recentAmount: amount }
      }
    );

    console.log(`✅ Successfully updated balance for ${email}`);
    console.log(`   Previous balance: ${currentBalance}`);
    console.log(`   Amount added: ${amount}`);
    console.log(`   New balance: ${newBalance}`);

    return true;

  } catch (error) {
    console.error('❌ Error updating user balance:', error.message);
    return false;
  } finally {
    // Close MongoDB connection
    await mongoose.connection.close();
    console.log('MongoDB connection closed');
  }
}

/**
 * Main function to handle command line arguments
 */
async function main() {
  const args = process.argv.slice(2);

  if (args.length < 2) {
    console.log('Usage: node increase_balance.js <email> <amount>');
    console.log('Example: node increase_balance.js <EMAIL> 100');
    process.exit(1);
  }

  const email = args[0];
  const amount = parseFloat(args[1]);

  if (isNaN(amount)) {
    console.log('❌ Amount must be a valid number');
    process.exit(1);
  }

  if (amount <= 0) {
    console.log('❌ Amount must be greater than 0');
    process.exit(1);
  }

  console.log(`Starting balance increase for ${email} with amount ${amount}`);
  
  const success = await increaseUserBalance(email, amount);
  
  if (success) {
    console.log('✅ Operation completed successfully');
    process.exit(0);
  } else {
    console.log('❌ Operation failed');
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = { increaseUserBalance };
