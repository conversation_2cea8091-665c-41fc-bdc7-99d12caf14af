# Balance Management Scripts

## Increase User Balance Script

This script allows you to increase a user's balance by their email address.

### Usage

```bash
cd back_app
node scripts/increase_balance.js <email> <amount>
```

### Examples

```bash
# Add 100 to user's balance
node scripts/increase_balance.js <EMAIL> 100

# Add 50.5 to user's balance
node scripts/increase_balance.js <EMAIL> 50.5
```

### Features

- ✅ Connects to MongoDB using the provided connection string
- ✅ Finds user by email address
- ✅ Increases balance by specified amount
- ✅ Updates recentAmount field
- ✅ Provides detailed feedback and error handling
- ✅ Validates input parameters
- ✅ Safely closes database connection

### Error Handling

The script will handle the following scenarios:
- User not found by email
- Invalid amount (non-numeric or negative)
- Database connection errors
- Missing command line arguments

### Output

The script provides clear feedback:
- ✅ Success messages with balance details
- ❌ Error messages for failures
- Connection status updates
